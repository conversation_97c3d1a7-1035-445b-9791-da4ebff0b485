-- Create the trigger to automatically create profiles when users sign up
CREATE OR REPLACE FUNCTION public.handle_new_user()
R<PERSON><PERSON><PERSON> trigger
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = ''
AS $$
BEGIN
  INSERT INTO public.profiles (id, full_name, workspace_id)
  VALUES (
    NEW.id, 
    NEW.raw_user_meta_data ->> 'full_name',
    gen_random_uuid() -- Create a new workspace for each user initially
  );
  
  -- Also create a default workspace for the user
  INSERT INTO public.workspaces (id, name, slug, created_at, updated_at)
  VALUES (
    (SELECT workspace_id FROM public.profiles WHERE id = NEW.id),
    COALESCE(NEW.raw_user_meta_data ->> 'full_name', 'Mi Empresa') || ' - Workspace',
    'workspace-' || substr(NEW.id::text, 1, 8),
    now(),
    now()
  );
  
  RETURN NEW;
END;
$$;

-- Create trigger on auth.users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();