import { useState } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import {
  Calendar,
  Users,
  BarChart3,
  Bot,
  DollarSign,
  Building2,
  Bell,
  Settings,
  ChevronLeft,
  Zap,
} from 'lucide-react';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  useSidebar,
} from '@/components/ui/sidebar';

const navigationItems = [
  {
    title: 'Panel Principal',
    items: [
      { title: 'Dashboard', url: '/dashboard', icon: BarChart3 },
      { title: 'Citas', url: '/appointments', icon: Calendar },
      { title: 'Clientes', url: '/clients', icon: Users },
    ]
  },
  {
    title: 'Inteligencia Artificial',
    items: [
      { title: 'I.R.A. Monitor', url: '/ai-interactions', icon: Bot },
      { title: 'Costos & Consumo', url: '/ai-costs', icon: DollarSign },
    ]
  },
  {
    title: 'Gestión',
    items: [
      { title: 'Workspaces', url: '/workspaces', icon: Building2 },
      { title: 'Notificaciones', url: '/notifications', icon: Bell },
      { title: 'Configuración', url: '/settings', icon: Settings },
    ]
  }
];

export function AppSidebar() {
  const { state: sidebarState } = useSidebar();
  const collapsed = sidebarState === 'collapsed';
  const location = useLocation();
  const currentPath = location.pathname;

  const isActive = (path: string) => currentPath === path || currentPath.startsWith(path + '/');

  return (
    <Sidebar className={`transition-all duration-300 ${collapsed ? 'w-16' : 'w-64'}`}>
      <SidebarHeader className="border-b border-border/50 p-4">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
            <Zap className="h-5 w-5 text-white" />
          </div>
          {!collapsed && (
            <div>
              <h2 className="font-bold text-lg text-foreground">YoAtiendo</h2>
              <p className="text-xs text-muted-foreground">IA Operativa</p>
            </div>
          )}
        </div>
      </SidebarHeader>

      <SidebarContent className="p-2">
        {navigationItems.map((group) => (
          <SidebarGroup key={group.title}>
            {!collapsed && (
              <SidebarGroupLabel className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-2">
                {group.title}
              </SidebarGroupLabel>
            )}
            <SidebarGroupContent>
              <SidebarMenu>
                {group.items.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild>
                      <NavLink
                        to={item.url}
                        className={`flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200 ${
                          isActive(item.url)
                            ? 'bg-primary text-primary-foreground shadow-soft'
                            : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
                        }`}
                      >
                        <item.icon className="h-5 w-5 flex-shrink-0" />
                        {!collapsed && <span className="font-medium">{item.title}</span>}
                      </NavLink>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>
    </Sidebar>
  );
}