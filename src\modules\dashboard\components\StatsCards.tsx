import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Calendar,
  Users,
  DollarSign,
  Clock,
  TrendingUp,
  Bo<PERSON>,
  Al<PERSON>Triangle,
  CheckCircle,
} from 'lucide-react';

interface StatCard {
  title: string;
  value: string;
  subtitle: string;
  icon: React.ComponentType<any>;
  trend?: {
    value: string;
    positive: boolean;
  };
  badge?: {
    text: string;
    variant: 'default' | 'success' | 'warning' | 'destructive';
  };
}

const statsData: StatCard[] = [
  {
    title: 'Citas Hoy',
    value: '12',
    subtitle: '3 confirmadas • 9 pendientes',
    icon: Calendar,
    trend: { value: '+15%', positive: true },
    badge: { text: '3 nuevas', variant: 'success' },
  },
  {
    title: 'Clientes Activos',
    value: '284',
    subtitle: '12 nuevos este mes',
    icon: Users,
    trend: { value: '+8%', positive: true },
  },
  {
    title: 'Ingresos del Mes',
    value: '$45,280',
    subtitle: 'Meta: $50,000',
    icon: DollarSign,
    trend: { value: '+22%', positive: true },
  },
  {
    title: 'Tiempo Ahorrado',
    value: '127h',
    subtitle: 'Por automatización IA',
    icon: Clock,
    badge: { text: 'Este mes', variant: 'default' },
  },
  {
    title: 'Conversiones IA',
    value: '89%',
    subtitle: 'Citas confirmadas por IA',
    icon: Bot,
    trend: { value: '+5%', positive: true },
    badge: { text: 'Excelente', variant: 'success' },
  },
  {
    title: 'Intervenciones',
    value: '3',
    subtitle: 'Casos que necesitan atención',
    icon: AlertTriangle,
    badge: { text: 'Pendientes', variant: 'warning' },
  },
  {
    title: 'Satisfacción',
    value: '4.8/5',
    subtitle: 'Basado en 156 reseñas',
    icon: CheckCircle,
    trend: { value: '+0.2', positive: true },
  },
  {
    title: 'ROI Mensual',
    value: '340%',
    subtitle: 'Retorno de inversión',
    icon: TrendingUp,
    trend: { value: '+15%', positive: true },
    badge: { text: 'Objetivo cumplido', variant: 'success' },
  },
];

export function StatsCards() {
  const getBadgeVariant = (variant: string) => {
    switch (variant) {
      case 'success':
        return 'default'; // Will use success color from CSS
      case 'warning':
        return 'secondary';
      case 'destructive':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statsData.map((stat, index) => (
        <Card key={index} className="card-premium hover-lift">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {stat.title}
            </CardTitle>
            <div className="flex items-center gap-2">
              {stat.badge && (
                <Badge
                  variant={getBadgeVariant(stat.badge.variant)}
                  className={`text-xs ${
                    stat.badge.variant === 'success'
                      ? 'bg-green-100 text-green-800 border-green-200'
                      : stat.badge.variant === 'warning'
                      ? 'bg-amber-100 text-amber-800 border-amber-200'
                      : ''
                  }`}
                >
                  {stat.badge.text}
                </Badge>
              )}
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="text-2xl font-bold text-foreground">
                  {stat.value}
                </div>
                {stat.trend && (
                  <Badge
                    variant="outline"
                    className={`text-xs ${
                      stat.trend.positive
                        ? 'bg-green-50 text-green-700 border-green-200'
                        : 'bg-red-50 text-red-700 border-red-200'
                    }`}
                  >
                    {stat.trend.value}
                  </Badge>
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                {stat.subtitle}
              </p>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}