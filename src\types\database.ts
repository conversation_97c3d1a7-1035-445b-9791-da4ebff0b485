export type AppointmentStatus = 'scheduled' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show';
export type AppointmentOrigin = 'manual' | 'ai_whatsapp' | 'ai_voice' | 'web_form';
export type InteractionStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'requires_intervention';
export type InteractionChannel = 'whatsapp' | 'voice' | 'web_chat';

export interface Workspace {
  id: string;
  name: string;
  slug: string;
  logo_url?: string;
  industry?: string;
  business_hours: Record<string, { open: string; close: string; enabled: boolean }>;
  contact_info: { phone: string; email: string; address: string };
  ai_agent_config: { personality: string; language: string; custom_instructions: string };
  knowledge_sources: any[];
  subscription_plan: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Profile {
  id: string;
  workspace_id: string;
  full_name: string;
  avatar_url?: string;
  role: string;
  phone?: string;
  preferences: {
    language: string;
    timezone: string;
    notifications: { email: boolean; push: boolean };
  };
  last_active_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Client {
  id: string;
  workspace_id: string;
  name: string;
  email?: string;
  phone: string;
  address?: string;
  notes?: string;
  tags: string[];
  preferences: { preferred_time?: string; preferred_service?: string };
  total_appointments: number;
  last_appointment_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Service {
  id: string;
  workspace_id: string;
  name: string;
  description?: string;
  duration_minutes: number;
  price?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Appointment {
  id: string;
  workspace_id: string;
  client_id: string;
  service_id?: string;
  title: string;
  description?: string;
  start_time: string;
  end_time: string;
  status: AppointmentStatus;
  origin: AppointmentOrigin;
  notes?: string;
  ai_interaction_id?: string;
  cancellation_reason?: string;
  created_by?: string;
  created_at: string;
  updated_at: string;
  client?: Client;
  service?: Service;
}

export interface AIInteraction {
  id: string;
  workspace_id: string;
  client_id?: string;
  appointment_id?: string;
  channel: InteractionChannel;
  status: InteractionStatus;
  transcript?: string;
  intent_detected?: string;
  confidence_score?: number;
  cost_tokens: number;
  cost_minutes: number;
  cost_usd: number;
  requires_intervention: boolean;
  intervention_reason?: string;
  resolved_by?: string;
  resolved_at?: string;
  metadata: any;
  created_at: string;
  updated_at: string;
  client?: Client;
  appointment?: Appointment;
}

export interface WorkspaceAnalytics {
  id: string;
  workspace_id: string;
  date: string;
  total_appointments: number;
  completed_appointments: number;
  cancelled_appointments: number;
  ai_generated_appointments: number;
  revenue_generated: number;
  ai_cost_usd: number;
  time_saved_minutes: number;
  after_hours_opportunities: number;
  created_at: string;
}

export interface Notification {
  id: string;
  workspace_id: string;
  user_id?: string;
  title: string;
  message: string;
  type: string;
  read_at?: string;
  action_url?: string;
  created_at: string;
}