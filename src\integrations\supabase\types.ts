export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "13.0.4"
  }
  public: {
    Tables: {
      ai_interactions: {
        Row: {
          appointment_id: string | null
          channel: Database["public"]["Enums"]["interaction_channel"]
          client_id: string | null
          confidence_score: number | null
          cost_minutes: number | null
          cost_tokens: number | null
          cost_usd: number | null
          created_at: string
          id: string
          intent_detected: string | null
          intervention_reason: string | null
          metadata: Json | null
          requires_intervention: boolean | null
          resolved_at: string | null
          resolved_by: string | null
          status: Database["public"]["Enums"]["interaction_status"] | null
          transcript: string | null
          updated_at: string
          workspace_id: string
        }
        Insert: {
          appointment_id?: string | null
          channel: Database["public"]["Enums"]["interaction_channel"]
          client_id?: string | null
          confidence_score?: number | null
          cost_minutes?: number | null
          cost_tokens?: number | null
          cost_usd?: number | null
          created_at?: string
          id?: string
          intent_detected?: string | null
          intervention_reason?: string | null
          metadata?: Json | null
          requires_intervention?: boolean | null
          resolved_at?: string | null
          resolved_by?: string | null
          status?: Database["public"]["Enums"]["interaction_status"] | null
          transcript?: string | null
          updated_at?: string
          workspace_id: string
        }
        Update: {
          appointment_id?: string | null
          channel?: Database["public"]["Enums"]["interaction_channel"]
          client_id?: string | null
          confidence_score?: number | null
          cost_minutes?: number | null
          cost_tokens?: number | null
          cost_usd?: number | null
          created_at?: string
          id?: string
          intent_detected?: string | null
          intervention_reason?: string | null
          metadata?: Json | null
          requires_intervention?: boolean | null
          resolved_at?: string | null
          resolved_by?: string | null
          status?: Database["public"]["Enums"]["interaction_status"] | null
          transcript?: string | null
          updated_at?: string
          workspace_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "ai_interactions_appointment_id_fkey"
            columns: ["appointment_id"]
            isOneToOne: false
            referencedRelation: "appointments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ai_interactions_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ai_interactions_resolved_by_fkey"
            columns: ["resolved_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "ai_interactions_workspace_id_fkey"
            columns: ["workspace_id"]
            isOneToOne: false
            referencedRelation: "workspaces"
            referencedColumns: ["id"]
          },
        ]
      }
      appointments: {
        Row: {
          ai_interaction_id: string | null
          cancellation_reason: string | null
          client_id: string
          created_at: string
          created_by: string | null
          description: string | null
          end_time: string
          id: string
          notes: string | null
          origin: Database["public"]["Enums"]["appointment_origin"] | null
          service_id: string | null
          start_time: string
          status: Database["public"]["Enums"]["appointment_status"] | null
          title: string
          updated_at: string
          workspace_id: string
        }
        Insert: {
          ai_interaction_id?: string | null
          cancellation_reason?: string | null
          client_id: string
          created_at?: string
          created_by?: string | null
          description?: string | null
          end_time: string
          id?: string
          notes?: string | null
          origin?: Database["public"]["Enums"]["appointment_origin"] | null
          service_id?: string | null
          start_time: string
          status?: Database["public"]["Enums"]["appointment_status"] | null
          title: string
          updated_at?: string
          workspace_id: string
        }
        Update: {
          ai_interaction_id?: string | null
          cancellation_reason?: string | null
          client_id?: string
          created_at?: string
          created_by?: string | null
          description?: string | null
          end_time?: string
          id?: string
          notes?: string | null
          origin?: Database["public"]["Enums"]["appointment_origin"] | null
          service_id?: string | null
          start_time?: string
          status?: Database["public"]["Enums"]["appointment_status"] | null
          title?: string
          updated_at?: string
          workspace_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "appointments_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_service_id_fkey"
            columns: ["service_id"]
            isOneToOne: false
            referencedRelation: "services"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "appointments_workspace_id_fkey"
            columns: ["workspace_id"]
            isOneToOne: false
            referencedRelation: "workspaces"
            referencedColumns: ["id"]
          },
        ]
      }
      clients: {
        Row: {
          address: string | null
          created_at: string
          email: string | null
          id: string
          last_appointment_at: string | null
          name: string
          notes: string | null
          phone: string
          preferences: Json | null
          tags: string[] | null
          total_appointments: number | null
          updated_at: string
          workspace_id: string
        }
        Insert: {
          address?: string | null
          created_at?: string
          email?: string | null
          id?: string
          last_appointment_at?: string | null
          name: string
          notes?: string | null
          phone: string
          preferences?: Json | null
          tags?: string[] | null
          total_appointments?: number | null
          updated_at?: string
          workspace_id: string
        }
        Update: {
          address?: string | null
          created_at?: string
          email?: string | null
          id?: string
          last_appointment_at?: string | null
          name?: string
          notes?: string | null
          phone?: string
          preferences?: Json | null
          tags?: string[] | null
          total_appointments?: number | null
          updated_at?: string
          workspace_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "clients_workspace_id_fkey"
            columns: ["workspace_id"]
            isOneToOne: false
            referencedRelation: "workspaces"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          action_url: string | null
          created_at: string
          id: string
          message: string
          read_at: string | null
          title: string
          type: string | null
          user_id: string | null
          workspace_id: string
        }
        Insert: {
          action_url?: string | null
          created_at?: string
          id?: string
          message: string
          read_at?: string | null
          title: string
          type?: string | null
          user_id?: string | null
          workspace_id: string
        }
        Update: {
          action_url?: string | null
          created_at?: string
          id?: string
          message?: string
          read_at?: string | null
          title?: string
          type?: string | null
          user_id?: string | null
          workspace_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_workspace_id_fkey"
            columns: ["workspace_id"]
            isOneToOne: false
            referencedRelation: "workspaces"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string
          full_name: string
          id: string
          last_active_at: string | null
          phone: string | null
          preferences: Json | null
          role: string | null
          updated_at: string
          workspace_id: string
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          full_name: string
          id: string
          last_active_at?: string | null
          phone?: string | null
          preferences?: Json | null
          role?: string | null
          updated_at?: string
          workspace_id: string
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          full_name?: string
          id?: string
          last_active_at?: string | null
          phone?: string | null
          preferences?: Json | null
          role?: string | null
          updated_at?: string
          workspace_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "profiles_workspace_id_fkey"
            columns: ["workspace_id"]
            isOneToOne: false
            referencedRelation: "workspaces"
            referencedColumns: ["id"]
          },
        ]
      }
      services: {
        Row: {
          created_at: string
          description: string | null
          duration_minutes: number
          id: string
          is_active: boolean | null
          name: string
          price: number | null
          updated_at: string
          workspace_id: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          duration_minutes?: number
          id?: string
          is_active?: boolean | null
          name: string
          price?: number | null
          updated_at?: string
          workspace_id: string
        }
        Update: {
          created_at?: string
          description?: string | null
          duration_minutes?: number
          id?: string
          is_active?: boolean | null
          name?: string
          price?: number | null
          updated_at?: string
          workspace_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "services_workspace_id_fkey"
            columns: ["workspace_id"]
            isOneToOne: false
            referencedRelation: "workspaces"
            referencedColumns: ["id"]
          },
        ]
      }
      workspace_analytics: {
        Row: {
          after_hours_opportunities: number | null
          ai_cost_usd: number | null
          ai_generated_appointments: number | null
          cancelled_appointments: number | null
          completed_appointments: number | null
          created_at: string
          date: string
          id: string
          revenue_generated: number | null
          time_saved_minutes: number | null
          total_appointments: number | null
          workspace_id: string
        }
        Insert: {
          after_hours_opportunities?: number | null
          ai_cost_usd?: number | null
          ai_generated_appointments?: number | null
          cancelled_appointments?: number | null
          completed_appointments?: number | null
          created_at?: string
          date: string
          id?: string
          revenue_generated?: number | null
          time_saved_minutes?: number | null
          total_appointments?: number | null
          workspace_id: string
        }
        Update: {
          after_hours_opportunities?: number | null
          ai_cost_usd?: number | null
          ai_generated_appointments?: number | null
          cancelled_appointments?: number | null
          completed_appointments?: number | null
          created_at?: string
          date?: string
          id?: string
          revenue_generated?: number | null
          time_saved_minutes?: number | null
          total_appointments?: number | null
          workspace_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "workspace_analytics_workspace_id_fkey"
            columns: ["workspace_id"]
            isOneToOne: false
            referencedRelation: "workspaces"
            referencedColumns: ["id"]
          },
        ]
      }
      workspaces: {
        Row: {
          ai_agent_config: Json | null
          business_hours: Json | null
          contact_info: Json | null
          created_at: string
          id: string
          industry: string | null
          is_active: boolean | null
          knowledge_sources: Json | null
          logo_url: string | null
          name: string
          slug: string
          subscription_plan: string | null
          updated_at: string
        }
        Insert: {
          ai_agent_config?: Json | null
          business_hours?: Json | null
          contact_info?: Json | null
          created_at?: string
          id?: string
          industry?: string | null
          is_active?: boolean | null
          knowledge_sources?: Json | null
          logo_url?: string | null
          name: string
          slug: string
          subscription_plan?: string | null
          updated_at?: string
        }
        Update: {
          ai_agent_config?: Json | null
          business_hours?: Json | null
          contact_info?: Json | null
          created_at?: string
          id?: string
          industry?: string | null
          is_active?: boolean | null
          knowledge_sources?: Json | null
          logo_url?: string | null
          name?: string
          slug?: string
          subscription_plan?: string | null
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      appointment_origin: "manual" | "ai_whatsapp" | "ai_voice" | "web_form"
      appointment_status:
        | "scheduled"
        | "confirmed"
        | "in_progress"
        | "completed"
        | "cancelled"
        | "no_show"
      interaction_channel: "whatsapp" | "voice" | "web_chat"
      interaction_status:
        | "pending"
        | "processing"
        | "completed"
        | "failed"
        | "requires_intervention"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      appointment_origin: ["manual", "ai_whatsapp", "ai_voice", "web_form"],
      appointment_status: [
        "scheduled",
        "confirmed",
        "in_progress",
        "completed",
        "cancelled",
        "no_show",
      ],
      interaction_channel: ["whatsapp", "voice", "web_chat"],
      interaction_status: [
        "pending",
        "processing",
        "completed",
        "failed",
        "requires_intervention",
      ],
    },
  },
} as const
