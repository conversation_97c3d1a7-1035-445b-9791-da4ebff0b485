// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://ftqvbnxnshidpchauzgc.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ0cXZibnhuc2hpZHBjaGF1emdjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ5MjQwODAsImV4cCI6MjA3MDUwMDA4MH0.2SNhBdx-vTWP4W8AefinvBCS0N1nqTrZreiVxCOI0Y4";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});