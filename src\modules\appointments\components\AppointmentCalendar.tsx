import { useState } from 'react';
import { Calendar, dateFnsLocalizer, View, Views } from 'react-big-calendar';
import { format, parse, startOfWeek, getDay } from 'date-fns';
import { es } from 'date-fns/locale';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CalendarDays, ChevronLeft, ChevronRight, Plus } from 'lucide-react';
import 'react-big-calendar/lib/css/react-big-calendar.css';

const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek,
  getDay,
  locales: { es },
});

interface AppointmentEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  client_name: string;
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled';
  origin: 'manual' | 'ai' | 'online';
}

const mockEvents: AppointmentEvent[] = [
  {
    id: '1',
    title: '<PERSON><PERSON>',
    start: new Date(2024, 10, 15, 10, 0),
    end: new Date(2024, 10, 15, 11, 0),
    client_name: '<PERSON>',
    status: 'confirmed',
    origin: 'ai',
  },
  {
    id: '2',
    title: 'Revisión Ortodoncia',
    start: new Date(2024, 10, 15, 14, 30),
    end: new Date(2024, 10, 15, 15, 30),
    client_name: 'Carlos López',
    status: 'scheduled',
    origin: 'manual',
  },
];

export function AppointmentCalendar() {
  const [view, setView] = useState<View>('week');
  const [date, setDate] = useState(new Date());

  const handleNavigate = (newDate: Date) => {
    setDate(newDate);
  };

  const handleViewChange = (newView: View) => {
    setView(newView);
  };

  const eventPropGetter = (event: AppointmentEvent) => {
    let backgroundColor = '';
    let color = 'white';

    switch (event.status) {
      case 'confirmed':
        backgroundColor = 'hsl(var(--success))';
        break;
      case 'scheduled':
        backgroundColor = 'hsl(var(--primary))';
        break;
      case 'completed':
        backgroundColor = 'hsl(var(--muted))';
        color = 'hsl(var(--muted-foreground))';
        break;
      case 'cancelled':
        backgroundColor = 'hsl(var(--destructive))';
        break;
      default:
        backgroundColor = 'hsl(var(--primary))';
    }

    return {
      style: {
        backgroundColor,
        color,
        border: 'none',
        borderRadius: '6px',
        fontSize: '12px',
        padding: '2px 6px',
      },
    };
  };

  const CustomToolbar = () => (
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center gap-4">
        <h2 className="text-2xl font-bold text-foreground">
          {format(date, 'MMMM yyyy', { locale: es })}
        </h2>
        <div className="flex items-center gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleNavigate(new Date(date.getFullYear(), date.getMonth() - 1))}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleNavigate(new Date())}
          >
            Hoy
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleNavigate(new Date(date.getFullYear(), date.getMonth() + 1))}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <div className="flex items-center border rounded-lg">
          {[
            { key: Views.DAY, label: 'Día' },
            { key: Views.WEEK, label: 'Semana' },
            { key: Views.MONTH, label: 'Mes' },
          ].map((viewOption) => (
            <Button
              key={viewOption.key}
              variant={view === viewOption.key ? 'default' : 'ghost'}
              size="sm"
              onClick={() => handleViewChange(viewOption.key)}
              className="rounded-none first:rounded-l-md last:rounded-r-md"
            >
              {viewOption.label}
            </Button>
          ))}
        </div>
        <Button className="btn-premium">
          <Plus className="h-4 w-4 mr-2" />
          Nueva Cita
        </Button>
      </div>
    </div>
  );

  return (
    <Card className="card-premium">
      <CardHeader>
        <div className="flex items-center gap-2">
          <CalendarDays className="h-5 w-5 text-primary" />
          <CardTitle>Calendario de Citas</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <CustomToolbar />
        
        <div className="mb-4 flex items-center gap-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-primary"></div>
            <span className="text-sm text-muted-foreground">Programada</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-success"></div>
            <span className="text-sm text-muted-foreground">Confirmada</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-muted"></div>
            <span className="text-sm text-muted-foreground">Completada</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-destructive"></div>
            <span className="text-sm text-muted-foreground">Cancelada</span>
          </div>
        </div>

        <div className="calendar-container">
          <Calendar
            localizer={localizer}
            events={mockEvents}
            startAccessor="start"
            endAccessor="end"
            style={{ height: 600 }}
            view={view}
            date={date}
            onNavigate={handleNavigate}
            onView={handleViewChange}
            eventPropGetter={eventPropGetter}
            messages={{
              next: 'Siguiente',
              previous: 'Anterior',
              today: 'Hoy',
              month: 'Mes',
              week: 'Semana',
              day: 'Día',
              agenda: 'Agenda',
              date: 'Fecha',
              time: 'Hora',
              event: 'Evento',
              noEventsInRange: 'No hay citas en este rango de fechas',
            }}
            toolbar={false}
          />
        </div>
      </CardContent>
    </Card>
  );
}