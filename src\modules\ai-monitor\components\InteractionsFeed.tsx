import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Bot,
  Phone,
  MessageCircle,
  Clock,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Expand,
  Filter,
} from 'lucide-react';

interface AIInteraction {
  id: string;
  timestamp: string;
  channel: 'whatsapp' | 'voice' | 'web';
  clientName: string;
  intent: string;
  status: 'pending' | 'completed' | 'requires_intervention';
  duration: string;
  cost: number;
  confidence: number;
  appointmentCreated?: boolean;
  transcript: string;
}

const mockInteractions: AIInteraction[] = [
  {
    id: '1',
    timestamp: '2024-11-15 14:30:25',
    channel: 'whatsapp',
    clientName: '<PERSON>',
    intent: 'Agendar cita',
    status: 'completed',
    duration: '2m 15s',
    cost: 0.05,
    confidence: 95,
    appointmentCreated: true,
    transcript: 'Cliente solicitó cita para limpieza dental. Se agendó para el 20 de noviembre a las 10:00 AM.',
  },
  {
    id: '2',
    timestamp: '2024-11-15 14:25:10',
    channel: 'voice',
    clientName: 'Carlos Ruiz',
    intent: 'Cambio de cita',
    status: 'requires_intervention',
    duration: '4m 32s',
    cost: 0.12,
    confidence: 72,
    appointmentCreated: false,
    transcript: 'Cliente solicita cambio urgente por emergencia familiar. Requiere confirmación manual.',
  },
  {
    id: '3',
    timestamp: '2024-11-15 14:20:45',
    channel: 'whatsapp',
    clientName: 'Ana López',
    intent: 'Información de servicios',
    status: 'completed',
    duration: '1m 45s',
    cost: 0.03,
    confidence: 88,
    appointmentCreated: false,
    transcript: 'Consultó precios de ortodoncia. Se proporcionó información completa.',
  },
  {
    id: '4',
    timestamp: '2024-11-15 14:15:30',
    channel: 'web',
    clientName: 'Roberto Méndez',
    intent: 'Cancelación',
    status: 'pending',
    duration: '3m 20s',
    cost: 0.08,
    confidence: 82,
    appointmentCreated: false,
    transcript: 'Procesando solicitud de cancelación...',
  },
];

export function InteractionsFeed() {
  const [selectedChannel, setSelectedChannel] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [expandedInteraction, setExpandedInteraction] = useState<string | null>(null);

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'whatsapp':
        return <MessageCircle className="h-4 w-4 text-green-600" />;
      case 'voice':
        return <Phone className="h-4 w-4 text-blue-600" />;
      case 'web':
        return <Bot className="h-4 w-4 text-purple-600" />;
      default:
        return <Bot className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <Badge className="bg-green-100 text-green-800 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Completada
          </Badge>
        );
      case 'requires_intervention':
        return (
          <Badge className="bg-amber-100 text-amber-800 border-amber-200">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Requiere atención
          </Badge>
        );
      case 'pending':
        return (
          <Badge className="bg-blue-100 text-blue-800 border-blue-200">
            <Clock className="h-3 w-3 mr-1" />
            En proceso
          </Badge>
        );
      default:
        return null;
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return 'text-green-600';
    if (confidence >= 70) return 'text-amber-600';
    return 'text-red-600';
  };

  const filteredInteractions = mockInteractions.filter(interaction => {
    const channelMatch = selectedChannel === 'all' || interaction.channel === selectedChannel;
    const statusMatch = selectedStatus === 'all' || interaction.status === selectedStatus;
    return channelMatch && statusMatch;
  });

  return (
    <Card className="card-premium">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Bot className="h-5 w-5 text-primary" />
            <CardTitle>Monitor I.R.A. - Tiempo Real</CardTitle>
            <Badge variant="outline" className="ml-2">
              {filteredInteractions.length} interacciones
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            <Select value={selectedChannel} onValueChange={setSelectedChannel}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Canal" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los canales</SelectItem>
                <SelectItem value="whatsapp">WhatsApp</SelectItem>
                <SelectItem value="voice">Voz</SelectItem>
                <SelectItem value="web">Web</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Estado" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos</SelectItem>
                <SelectItem value="completed">Completadas</SelectItem>
                <SelectItem value="pending">En proceso</SelectItem>
                <SelectItem value="requires_intervention">Requieren atención</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Más filtros
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {filteredInteractions.map((interaction) => (
            <div
              key={interaction.id}
              className="border rounded-lg p-4 hover:bg-muted/50 transition-colors"
            >
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback className="bg-gradient-primary text-white">
                      {interaction.clientName
                        .split(' ')
                        .map(n => n[0])
                        .join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium text-foreground">
                        {interaction.clientName}
                      </h4>
                      {getChannelIcon(interaction.channel)}
                      {interaction.appointmentCreated && (
                        <Badge className="bg-green-100 text-green-800 border-green-200 text-xs">
                          Cita creada
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                      <span>{interaction.intent}</span>
                      <span>•</span>
                      <span>{interaction.timestamp}</span>
                      <span>•</span>
                      <span>{interaction.duration}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="text-right">
                    <div className="flex items-center gap-2">
                      {getStatusBadge(interaction.status)}
                    </div>
                    <div className="flex items-center gap-2 mt-1 text-sm">
                      <span className={`font-medium ${getConfidenceColor(interaction.confidence)}`}>
                        {interaction.confidence}% confianza
                      </span>
                      <span className="text-muted-foreground">•</span>
                      <span className="flex items-center gap-1 text-muted-foreground">
                        <DollarSign className="h-3 w-3" />
                        ${interaction.cost.toFixed(3)}
                      </span>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() =>
                      setExpandedInteraction(
                        expandedInteraction === interaction.id ? null : interaction.id
                      )
                    }
                  >
                    <Expand className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {expandedInteraction === interaction.id && (
                <div className="mt-4 pt-4 border-t">
                  <h5 className="font-medium text-sm text-muted-foreground mb-2">
                    Transcripción:
                  </h5>
                  <div className="bg-muted/50 rounded-lg p-3 text-sm">
                    {interaction.transcript}
                  </div>
                  {interaction.status === 'requires_intervention' && (
                    <div className="mt-3 flex gap-2">
                      <Button size="sm" className="btn-premium">
                        Atender caso
                      </Button>
                      <Button variant="outline" size="sm">
                        Marcar como resuelto
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>

        {filteredInteractions.length === 0 && (
          <div className="text-center py-8">
            <Bot className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium text-muted-foreground mb-2">
              No hay interacciones
            </h3>
            <p className="text-muted-foreground">
              Las interacciones de IA aparecerán aquí en tiempo real.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}