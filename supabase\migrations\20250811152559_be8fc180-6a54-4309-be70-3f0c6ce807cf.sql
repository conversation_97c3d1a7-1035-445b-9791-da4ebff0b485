-- YoAtiendo Multi-tenant Database Schema
-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create enum types
CREATE TYPE appointment_status AS ENUM ('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show');
CREATE TYPE appointment_origin AS ENUM ('manual', 'ai_whatsapp', 'ai_voice', 'web_form');
CREATE TYPE interaction_status AS ENUM ('pending', 'processing', 'completed', 'failed', 'requires_intervention');
CREATE TYPE interaction_channel AS ENUM ('whatsapp', 'voice', 'web_chat');

-- Workspaces (Multi-tenant)
CREATE TABLE public.workspaces (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  logo_url TEXT,
  industry TEXT,
  business_hours JSONB DEFAULT '{"monday": {"open": "09:00", "close": "18:00", "enabled": true}, "tuesday": {"open": "09:00", "close": "18:00", "enabled": true}, "wednesday": {"open": "09:00", "close": "18:00", "enabled": true}, "thursday": {"open": "09:00", "close": "18:00", "enabled": true}, "friday": {"open": "09:00", "close": "18:00", "enabled": true}, "saturday": {"open": "09:00", "close": "14:00", "enabled": false}, "sunday": {"open": "09:00", "close": "14:00", "enabled": false}}',
  contact_info JSONB DEFAULT '{"phone": "", "email": "", "address": ""}',
  ai_agent_config JSONB DEFAULT '{"personality": "professional", "language": "es", "custom_instructions": ""}',
  knowledge_sources JSONB DEFAULT '[]',
  subscription_plan TEXT DEFAULT 'free',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- User Profiles (extends auth.users)
CREATE TABLE public.profiles (
  id UUID NOT NULL PRIMARY KEY,
  workspace_id UUID NOT NULL REFERENCES public.workspaces(id) ON DELETE CASCADE,
  full_name TEXT NOT NULL,
  avatar_url TEXT,
  role TEXT DEFAULT 'admin',
  phone TEXT,
  preferences JSONB DEFAULT '{"language": "es", "timezone": "America/Mexico_City", "notifications": {"email": true, "push": true}}',
  last_active_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Clients
CREATE TABLE public.clients (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  workspace_id UUID NOT NULL REFERENCES public.workspaces(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  email TEXT,
  phone TEXT NOT NULL,
  address TEXT,
  notes TEXT,
  tags TEXT[] DEFAULT '{}',
  preferences JSONB DEFAULT '{"preferred_time": null, "preferred_service": null}',
  total_appointments INTEGER DEFAULT 0,
  last_appointment_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Services
CREATE TABLE public.services (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  workspace_id UUID NOT NULL REFERENCES public.workspaces(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  duration_minutes INTEGER NOT NULL DEFAULT 60,
  price DECIMAL(10,2),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Appointments
CREATE TABLE public.appointments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  workspace_id UUID NOT NULL REFERENCES public.workspaces(id) ON DELETE CASCADE,
  client_id UUID NOT NULL REFERENCES public.clients(id) ON DELETE CASCADE,
  service_id UUID REFERENCES public.services(id) ON DELETE SET NULL,
  title TEXT NOT NULL,
  description TEXT,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE NOT NULL,
  status appointment_status DEFAULT 'scheduled',
  origin appointment_origin DEFAULT 'manual',
  notes TEXT,
  ai_interaction_id UUID,
  cancellation_reason TEXT,
  created_by UUID REFERENCES public.profiles(id),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- AI Interactions (I.R.A. Module)
CREATE TABLE public.ai_interactions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  workspace_id UUID NOT NULL REFERENCES public.workspaces(id) ON DELETE CASCADE,
  client_id UUID REFERENCES public.clients(id) ON DELETE SET NULL,
  appointment_id UUID REFERENCES public.appointments(id) ON DELETE SET NULL,
  channel interaction_channel NOT NULL,
  status interaction_status DEFAULT 'pending',
  transcript TEXT,
  intent_detected TEXT,
  confidence_score DECIMAL(3,2),
  cost_tokens INTEGER DEFAULT 0,
  cost_minutes DECIMAL(10,2) DEFAULT 0,
  cost_usd DECIMAL(10,4) DEFAULT 0,
  requires_intervention BOOLEAN DEFAULT false,
  intervention_reason TEXT,
  resolved_by UUID REFERENCES public.profiles(id),
  resolved_at TIMESTAMP WITH TIME ZONE,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Workspace Analytics
CREATE TABLE public.workspace_analytics (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  workspace_id UUID NOT NULL REFERENCES public.workspaces(id) ON DELETE CASCADE,
  date DATE NOT NULL,
  total_appointments INTEGER DEFAULT 0,
  completed_appointments INTEGER DEFAULT 0,
  cancelled_appointments INTEGER DEFAULT 0,
  ai_generated_appointments INTEGER DEFAULT 0,
  revenue_generated DECIMAL(10,2) DEFAULT 0,
  ai_cost_usd DECIMAL(10,4) DEFAULT 0,
  time_saved_minutes INTEGER DEFAULT 0,
  after_hours_opportunities INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(workspace_id, date)
);

-- Notifications
CREATE TABLE public.notifications (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  workspace_id UUID NOT NULL REFERENCES public.workspaces(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT DEFAULT 'info',
  read_at TIMESTAMP WITH TIME ZONE,
  action_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.workspaces ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.services ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.workspace_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies for Workspaces
CREATE POLICY "Users can view their own workspace" ON public.workspaces
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.workspace_id = workspaces.id
    )
  );

CREATE POLICY "Users can update their own workspace" ON public.workspaces
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.workspace_id = workspaces.id
      AND profiles.role = 'admin'
    )
  );

-- RLS Policies for Profiles
CREATE POLICY "Users can view profiles in their workspace" ON public.profiles
  FOR SELECT USING (
    auth.uid() = id OR
    workspace_id = (
      SELECT workspace_id FROM public.profiles 
      WHERE id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

-- RLS Policies for Clients
CREATE POLICY "Users can manage clients in their workspace" ON public.clients
  FOR ALL USING (
    workspace_id = (
      SELECT workspace_id FROM public.profiles 
      WHERE id = auth.uid()
    )
  );

-- RLS Policies for Services
CREATE POLICY "Users can manage services in their workspace" ON public.services
  FOR ALL USING (
    workspace_id = (
      SELECT workspace_id FROM public.profiles 
      WHERE id = auth.uid()
    )
  );

-- RLS Policies for Appointments
CREATE POLICY "Users can manage appointments in their workspace" ON public.appointments
  FOR ALL USING (
    workspace_id = (
      SELECT workspace_id FROM public.profiles 
      WHERE id = auth.uid()
    )
  );

-- RLS Policies for AI Interactions
CREATE POLICY "Users can manage ai_interactions in their workspace" ON public.ai_interactions
  FOR ALL USING (
    workspace_id = (
      SELECT workspace_id FROM public.profiles 
      WHERE id = auth.uid()
    )
  );

-- RLS Policies for Workspace Analytics
CREATE POLICY "Users can view analytics for their workspace" ON public.workspace_analytics
  FOR SELECT USING (
    workspace_id = (
      SELECT workspace_id FROM public.profiles 
      WHERE id = auth.uid()
    )
  );

-- RLS Policies for Notifications
CREATE POLICY "Users can view their own notifications" ON public.notifications
  FOR SELECT USING (
    user_id = auth.uid() OR
    workspace_id = (
      SELECT workspace_id FROM public.profiles 
      WHERE id = auth.uid()
    )
  );

CREATE POLICY "Users can update their own notifications" ON public.notifications
  FOR UPDATE USING (user_id = auth.uid());

-- Create triggers for updated_at
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_workspaces_updated_at BEFORE UPDATE ON public.workspaces FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON public.profiles FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON public.clients FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_services_updated_at BEFORE UPDATE ON public.services FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_appointments_updated_at BEFORE UPDATE ON public.appointments FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
CREATE TRIGGER update_ai_interactions_updated_at BEFORE UPDATE ON public.ai_interactions FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Function to create profile after user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Users will need to be assigned to a workspace during onboarding
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Indexes for performance
CREATE INDEX idx_profiles_workspace_id ON public.profiles(workspace_id);
CREATE INDEX idx_clients_workspace_id ON public.clients(workspace_id);
CREATE INDEX idx_appointments_workspace_id ON public.appointments(workspace_id);
CREATE INDEX idx_appointments_start_time ON public.appointments(start_time);
CREATE INDEX idx_ai_interactions_workspace_id ON public.ai_interactions(workspace_id);
CREATE INDEX idx_ai_interactions_created_at ON public.ai_interactions(created_at);
CREATE INDEX idx_workspace_analytics_workspace_date ON public.workspace_analytics(workspace_id, date);