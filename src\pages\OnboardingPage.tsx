import React, { useState } from 'react';
import { Navigate, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Building2, 
  Clock, 
  Phone, 
  Bot, 
  BookOpen,
  Loader2,
  CheckCircle2,
  ArrowRight,
  Zap
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface OnboardingData {
  workspaceName: string;
  industry: string;
  phone: string;
  email: string;
  address: string;
  businessHours: Record<string, { open: string; close: string; enabled: boolean }>;
  agentPersonality: string;
  customInstructions: string;
  knowledgeSources: string;
}

export default function OnboardingPage() {
  const { user, profile } = useAuth();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<OnboardingData>({
    workspaceName: '',
    industry: '',
    phone: '',
    email: user?.email || '',
    address: '',
    businessHours: {
      monday: { open: '09:00', close: '18:00', enabled: true },
      tuesday: { open: '09:00', close: '18:00', enabled: true },
      wednesday: { open: '09:00', close: '18:00', enabled: true },
      thursday: { open: '09:00', close: '18:00', enabled: true },
      friday: { open: '09:00', close: '18:00', enabled: true },
      saturday: { open: '09:00', close: '14:00', enabled: false },
      sunday: { open: '09:00', close: '14:00', enabled: false },
    },
    agentPersonality: 'professional',
    customInstructions: '',
    knowledgeSources: '',
  });

  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  if (profile) {
    return <Navigate to="/dashboard" replace />;
  }

  const totalSteps = 5;
  const progress = (currentStep / totalSteps) * 100;

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = async () => {
    setLoading(true);
    try {
      // Create workspace
      const workspaceSlug = data.workspaceName.toLowerCase().replace(/\s+/g, '-');
      const { data: workspace, error: workspaceError } = await supabase
        .from('workspaces')
        .insert({
          name: data.workspaceName,
          slug: workspaceSlug,
          industry: data.industry,
          contact_info: {
            phone: data.phone,
            email: data.email,
            address: data.address,
          },
          business_hours: data.businessHours,
          ai_agent_config: {
            personality: data.agentPersonality,
            language: 'es',
            custom_instructions: data.customInstructions,
          },
          knowledge_sources: data.knowledgeSources ? [data.knowledgeSources] : [],
        })
        .select()
        .single();

      if (workspaceError) throw workspaceError;

      // Create user profile
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: user.id,
          workspace_id: workspace.id,
          full_name: user.user_metadata?.full_name || 'Usuario',
          role: 'admin',
        });

      if (profileError) throw profileError;

      toast({
        title: '¡Workspace creado!',
        description: 'Tu configuración se ha guardado correctamente.',
      });

      // Refresh the page to trigger auth state update
      window.location.href = '/dashboard';
    } catch (error) {
      console.error('Error creating workspace:', error);
      toast({
        title: 'Error',
        description: 'No se pudo crear el workspace. Intenta de nuevo.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const steps = [
    {
      title: 'Datos del Negocio',
      description: 'Información básica de tu empresa',
      icon: Building2,
    },
    {
      title: 'Horarios',
      description: 'Define tus horarios de atención',
      icon: Clock,
    },
    {
      title: 'Contacto',
      description: 'Información de contacto',
      icon: Phone,
    },
    {
      title: 'Agente IA',
      description: 'Configura tu asistente inteligente',
      icon: Bot,
    },
    {
      title: 'Base de Conocimiento',
      description: 'Fuentes de información para la IA',
      icon: BookOpen,
    },
  ];

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="workspaceName">Nombre de tu Negocio *</Label>
              <Input
                id="workspaceName"
                value={data.workspaceName}
                onChange={(e) => setData({ ...data, workspaceName: e.target.value })}
                placeholder="Mi Clínica Dental"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="industry">Sector/Industria</Label>
              <Input
                id="industry"
                value={data.industry}
                onChange={(e) => setData({ ...data, industry: e.target.value })}
                placeholder="Salud, Belleza, Consultoría, etc."
              />
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Define los horarios de atención de tu negocio
            </p>
            <div className="grid grid-cols-1 gap-3">
              {Object.entries(data.businessHours).map(([day, hours]) => {
                const h = hours as { open: string; close: string; enabled: boolean };
                return (
                <div key={day} className="flex items-center gap-4 p-3 border rounded-lg">
                  <div className="w-20 text-sm font-medium capitalize">
                    {day === 'monday' ? 'Lunes' :
                     day === 'tuesday' ? 'Martes' :
                     day === 'wednesday' ? 'Miércoles' :
                     day === 'thursday' ? 'Jueves' :
                     day === 'friday' ? 'Viernes' :
                     day === 'saturday' ? 'Sábado' : 'Domingo'}
                  </div>
                  <Input
                    type="time"
                    value={h.open}
                    onChange={(e) => setData({
                      ...data,
                      businessHours: {
                        ...data.businessHours,
                        [day]: { ...h, open: e.target.value }
                      }
                    })}
                    className="w-24"
                  />
                  <span>-</span>
                  <Input
                    type="time"
                    value={h.close}
                    onChange={(e) => setData({
                      ...data,
                      businessHours: {
                        ...data.businessHours,
                        [day]: { ...h, close: e.target.value }
                      }
                    })}
                    className="w-24"
                  />
                  <Badge variant={h.enabled ? "default" : "secondary"}>
                    {h.enabled ? 'Activo' : 'Cerrado'}
                  </Badge>
                </div>
              )})}
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="phone">Teléfono Principal *</Label>
              <Input
                id="phone"
                value={data.phone}
                onChange={(e) => setData({ ...data, phone: e.target.value })}
                placeholder="+52 55 1234 5678"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email de Contacto</Label>
              <Input
                id="email"
                type="email"
                value={data.email}
                onChange={(e) => setData({ ...data, email: e.target.value })}
                placeholder="<EMAIL>"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="address">Dirección</Label>
              <Textarea
                id="address"
                value={data.address}
                onChange={(e) => setData({ ...data, address: e.target.value })}
                placeholder="Calle Principal 123, Colonia Centro, Ciudad, CP 12345"
                rows={3}
              />
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Personalidad del Agente IA</Label>
              <div className="grid grid-cols-1 gap-2">
                {[
                  { value: 'professional', label: 'Profesional' },
                  { value: 'friendly', label: 'Amigable' },
                  { value: 'formal', label: 'Formal' },
                ].map((option) => (
                  <Button
                    key={option.value}
                    variant={data.agentPersonality === option.value ? "default" : "outline"}
                    onClick={() => setData({ ...data, agentPersonality: option.value })}
                    className="justify-start"
                  >
                    {option.label}
                  </Button>
                ))}
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="customInstructions">Instrucciones Personalizadas</Label>
              <Textarea
                id="customInstructions"
                value={data.customInstructions}
                onChange={(e) => setData({ ...data, customInstructions: e.target.value })}
                placeholder="Ej: Siempre mencionar que ofrecemos planes de financiamiento..."
                rows={4}
              />
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="knowledgeSources">Fuentes de Conocimiento</Label>
              <Textarea
                id="knowledgeSources"
                value={data.knowledgeSources}
                onChange={(e) => setData({ ...data, knowledgeSources: e.target.value })}
                placeholder="Describe los servicios, precios, políticas, etc. que debe conocer tu asistente IA..."
                rows={6}
              />
              <p className="text-xs text-muted-foreground">
                Incluye información sobre servicios, precios, políticas de cancelación, etc.
              </p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-subtle flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-2 mb-4">
            <div className="w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center">
              <Zap className="h-6 w-6 text-white" />
            </div>
            <h1 className="text-2xl font-bold">YoAtiendo</h1>
          </div>
          <h2 className="text-xl font-semibold mb-2">Configuración Inicial</h2>
          <p className="text-muted-foreground">
            Vamos a configurar tu workspace en 5 sencillos pasos
          </p>
        </div>

        <div className="mb-8">
          <Progress value={progress} className="h-2" />
          <div className="flex justify-between mt-2">
            <span className="text-sm text-muted-foreground">
              Paso {currentStep} de {totalSteps}
            </span>
            <span className="text-sm text-muted-foreground">
              {Math.round(progress)}% completado
            </span>
          </div>
        </div>

        <Card className="card-premium">
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                {React.createElement(steps[currentStep - 1].icon, {
                  className: "h-5 w-5 text-primary"
                })}
              </div>
              <div>
                <CardTitle>{steps[currentStep - 1].title}</CardTitle>
                <CardDescription>{steps[currentStep - 1].description}</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {renderStepContent()}

            <div className="flex justify-between pt-4">
              <Button
                variant="outline"
                onClick={handleBack}
                disabled={currentStep === 1}
              >
                Anterior
              </Button>
              
              {currentStep === totalSteps ? (
                <Button
                  onClick={handleComplete}
                  disabled={loading || !data.workspaceName || !data.phone}
                  className="btn-hero"
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creando...
                    </>
                  ) : (
                    <>
                      <CheckCircle2 className="mr-2 h-4 w-4" />
                      Completar Configuración
                    </>
                  )}
                </Button>
              ) : (
                <Button
                  onClick={handleNext}
                  disabled={currentStep === 1 && !data.workspaceName}
                >
                  Siguiente
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}